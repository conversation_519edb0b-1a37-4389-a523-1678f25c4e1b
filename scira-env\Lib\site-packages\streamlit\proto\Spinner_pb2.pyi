"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Spinner(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TEXT_FIELD_NUMBER: builtins.int
    CACHE_FIELD_NUMBER: builtins.int
    SHOW_TIME_FIELD_NUMBER: builtins.int
    text: builtins.str
    """A message to display while executing that block."""
    cache: builtins.bool
    """Whether spinner used in caching functions."""
    show_time: builtins.bool
    """Whether to show elapsed time next to the spinner text."""
    def __init__(
        self,
        *,
        text: builtins.str = ...,
        cache: builtins.bool = ...,
        show_time: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["cache", b"cache", "show_time", b"show_time", "text", b"text"]) -> None: ...

global___Spinner = Spinner
