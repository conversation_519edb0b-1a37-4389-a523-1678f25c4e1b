import{bw as v,r as l,cl as k,cq as w,cn as p}from"./index.CbQtRkVt.js";import{R as C,c as S,d as J,B as M}from"./base-input.DMlw5p7n.js";function _(e){"@babel/helpers - typeof";return _=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(e)}var U=["Root","StartEnhancer","EndEnhancer"],Z=["startEnhancer","endEnhancer","overrides"];function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},f.apply(this,arguments)}function O(e,t){return V(e)||Q(e,t)||K(e,t)||G()}function G(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function K(e,t){if(e){if(typeof e=="string")return j(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return j(e,t)}}function j(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Q(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,a=!1,i,u;try{for(r=r.call(e);!(o=(i=r.next()).done)&&(n.push(i.value),!(t&&n.length===t));o=!0);}catch(b){a=!0,u=b}finally{try{!o&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function V(e){if(Array.isArray(e))return e}function I(e,t){if(e==null)return{};var r=X(e,t),n,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function X(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,a;for(a=0;a<n.length;a++)o=n[a],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}function Y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ee(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function te(e,t,r){return t&&ee(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function re(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&m(e,t)}function m(e,t){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},m(e,t)}function ne(e){var t=ae();return function(){var n=h(e),o;if(t){var a=h(this).constructor;o=Reflect.construct(n,arguments,a)}else o=n.apply(this,arguments);return oe(this,o)}}function oe(e,t){if(t&&(_(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return y(e)}function y(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ae(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},h(e)}function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ie=function(e){re(r,e);var t=ne(r);function r(){var n;Y(this,r);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return n=t.call.apply(t,[this].concat(a)),d(y(n),"state",{isFocused:n.props.autoFocus||!1}),d(y(n),"onFocus",function(u){n.setState({isFocused:!0}),n.props.onFocus(u)}),d(y(n),"onBlur",function(u){n.setState({isFocused:!1}),n.props.onBlur(u)}),n}return te(r,[{key:"render",value:function(){var o=this.props,a=o.startEnhancer,i=o.endEnhancer,u=o.overrides,b=u.Root,A=u.StartEnhancer,B=u.EndEnhancer,F=I(u,U),T=I(o,Z),x=v(b,C),g=O(x,2),$=g[0],N=g[1],D=v(A,S),E=O(D,2),W=E[0],q=E[1],z=v(B,S),P=O(z,2),H=P[0],L=P[1],s=J(this.props,this.state);return l.createElement($,f({"data-baseweb":"input"},s,N,{$adjoined:R(a,i),$hasIconTrailing:this.props.clearable||this.props.type=="password"}),c(a)&&l.createElement(W,f({},s,q,{$position:w.start}),typeof a=="function"?a(s):a),l.createElement(M,f({},T,{overrides:F,adjoined:R(a,i),onFocus:this.onFocus,onBlur:this.onBlur})),c(i)&&l.createElement(H,f({},s,L,{$position:w.end}),typeof i=="function"?i(s):i))}}]),r}(l.Component);d(ie,"defaultProps",{autoComplete:"on",autoFocus:!1,disabled:!1,name:"",onBlur:function(){},onFocus:function(){},overrides:{},required:!1,size:k.default,startEnhancer:null,endEnhancer:null,clearable:!1,type:"text",readOnly:!1});function R(e,t){return c(e)&&c(t)?p.both:c(e)?p.left:c(t)?p.right:p.none}function c(e){return!!(e||e===0)}export{ie as I};
