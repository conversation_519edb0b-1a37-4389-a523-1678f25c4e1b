"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import streamlit.proto.WidthConfig_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SubtitleTrack(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LABEL_FIELD_NUMBER: builtins.int
    URL_FIELD_NUMBER: builtins.int
    label: builtins.str
    """Label for the subtitle e.g. "English" or "Spanish"."""
    url: builtins.str
    def __init__(
        self,
        *,
        label: builtins.str = ...,
        url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["label", b"label", "url", b"url"]) -> None: ...

global___SubtitleTrack = SubtitleTrack

@typing.final
class Video(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Type:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _TypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Video._Type.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNUSED: Video._Type.ValueType  # 0
        """This should always exist."""
        NATIVE: Video._Type.ValueType  # 1
        YOUTUBE_IFRAME: Video._Type.ValueType  # 2

    class Type(_Type, metaclass=_TypeEnumTypeWrapper): ...
    UNUSED: Video.Type.ValueType  # 0
    """This should always exist."""
    NATIVE: Video.Type.ValueType  # 1
    YOUTUBE_IFRAME: Video.Type.ValueType  # 2

    URL_FIELD_NUMBER: builtins.int
    START_TIME_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    SUBTITLES_FIELD_NUMBER: builtins.int
    END_TIME_FIELD_NUMBER: builtins.int
    LOOP_FIELD_NUMBER: builtins.int
    AUTOPLAY_FIELD_NUMBER: builtins.int
    MUTED_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    WIDTH_CONFIG_FIELD_NUMBER: builtins.int
    url: builtins.str
    """A url pointing to a video file"""
    start_time: builtins.int
    """The currentTime attribute of the HTML <video> tag's <source> subtag."""
    type: global___Video.Type.ValueType
    """Type affects how browser wraps the video in tags: plain HTML5, YouTube..."""
    end_time: builtins.int
    """The time at which the video should stop playing. If not specified, plays to the end."""
    loop: builtins.bool
    """Indicates whether the video should start over from the beginning once it ends."""
    autoplay: builtins.bool
    muted: builtins.bool
    id: builtins.str
    @property
    def subtitles(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SubtitleTrack]:
        """Repeated field for subtitle tracks"""

    @property
    def width_config(self) -> streamlit.proto.WidthConfig_pb2.WidthConfig: ...
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        start_time: builtins.int = ...,
        type: global___Video.Type.ValueType = ...,
        subtitles: collections.abc.Iterable[global___SubtitleTrack] | None = ...,
        end_time: builtins.int = ...,
        loop: builtins.bool = ...,
        autoplay: builtins.bool = ...,
        muted: builtins.bool = ...,
        id: builtins.str = ...,
        width_config: streamlit.proto.WidthConfig_pb2.WidthConfig | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_width_config", b"_width_config", "width_config", b"width_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_width_config", b"_width_config", "autoplay", b"autoplay", "end_time", b"end_time", "id", b"id", "loop", b"loop", "muted", b"muted", "start_time", b"start_time", "subtitles", b"subtitles", "type", b"type", "url", b"url", "width_config", b"width_config"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_width_config", b"_width_config"]) -> typing.Literal["width_config"] | None: ...

global___Video = Video
