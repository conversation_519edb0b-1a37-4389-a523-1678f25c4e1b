# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/NamedDataSet.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import DataFrame_pb2 as streamlit_dot_proto_dot_DataFrame__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/NamedDataSet.proto\x1a\x1fstreamlit/proto/DataFrame.proto\"H\n\x0cNamedDataSet\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08has_name\x18\x03 \x01(\x08\x12\x18\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\n.DataFrameB1\n\x1c\x63om.snowflake.apps.streamlitB\x11NamedDataSetProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.NamedDataSet_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\021NamedDataSetProto'
  _globals['_NAMEDDATASET']._serialized_start=71
  _globals['_NAMEDDATASET']._serialized_end=143
# @@protoc_insertion_point(module_scope)
