# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/NewSession.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import AppPage_pb2 as streamlit_dot_proto_dot_AppPage__pb2
from streamlit.proto import SessionStatus_pb2 as streamlit_dot_proto_dot_SessionStatus__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n streamlit/proto/NewSession.proto\x1a\x1dstreamlit/proto/AppPage.proto\x1a#streamlit/proto/SessionStatus.proto\"\xa5\x02\n\nNewSession\x12\x1f\n\ninitialize\x18\x01 \x01(\x0b\x32\x0b.Initialize\x12\x15\n\rscript_run_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x18\n\x10main_script_path\x18\x04 \x01(\t\x12\x17\n\x06\x63onfig\x18\x06 \x01(\x0b\x32\x07.Config\x12(\n\x0c\x63ustom_theme\x18\x07 \x01(\x0b\x32\x12.CustomThemeConfig\x12\x1b\n\tapp_pages\x18\x08 \x03(\x0b\x32\x08.AppPage\x12\x18\n\x10page_script_hash\x18\t \x01(\t\x12\x1d\n\x15\x66ragment_ids_this_run\x18\n \x03(\t\x12\x18\n\x10main_script_hash\x18\x0b \x01(\tJ\x04\x08\x05\x10\x06\"\xba\x01\n\nInitialize\x12\x1c\n\tuser_info\x18\x01 \x01(\x0b\x32\t.UserInfo\x12*\n\x10\x65nvironment_info\x18\x03 \x01(\x0b\x32\x10.EnvironmentInfo\x12&\n\x0esession_status\x18\x04 \x01(\x0b\x32\x0e.SessionStatus\x12\x14\n\x0c\x63ommand_line\x18\x05 \x01(\t\x12\x12\n\nsession_id\x18\x06 \x01(\t\x12\x10\n\x08is_hello\x18\x07 \x01(\x08\"\x97\x02\n\x06\x43onfig\x12\x1a\n\x12gather_usage_stats\x18\x02 \x01(\x08\x12\x1e\n\x16max_cached_message_age\x18\x03 \x01(\x05\x12\x14\n\x0cmapbox_token\x18\x04 \x01(\t\x12\x19\n\x11\x61llow_run_on_save\x18\x05 \x01(\x08\x12\x14\n\x0chide_top_bar\x18\x06 \x01(\x08\x12\x18\n\x10hide_sidebar_nav\x18\x07 \x01(\x08\x12)\n\x0ctoolbar_mode\x18\x08 \x01(\x0e\x32\x13.Config.ToolbarMode\"?\n\x0bToolbarMode\x12\x08\n\x04\x41UTO\x10\x00\x12\r\n\tDEVELOPER\x10\x01\x12\n\n\x06VIEWER\x10\x02\x12\x0b\n\x07MINIMAL\x10\x03J\x04\x08\x01\x10\x02\"\xbf\x0b\n\x11\x43ustomThemeConfig\x12\x15\n\rprimary_color\x18\x01 \x01(\t\x12\"\n\x1asecondary_background_color\x18\x02 \x01(\t\x12\x18\n\x10\x62\x61\x63kground_color\x18\x03 \x01(\t\x12\x12\n\ntext_color\x18\x04 \x01(\t\x12+\n\x04\x66ont\x18\x05 \x01(\x0e\x32\x1d.CustomThemeConfig.FontFamily\x12*\n\x04\x62\x61se\x18\x06 \x01(\x0e\x32\x1c.CustomThemeConfig.BaseTheme\x12\x1f\n\x17widget_background_color\x18\x07 \x01(\t\x12\x1b\n\x13widget_border_color\x18\x08 \x01(\t\x12\x15\n\x05radii\x18\t \x01(\x0b\x32\x06.Radii\x12\x14\n\x0cheading_font\x18\x0c \x01(\t\x12\x11\n\tbody_font\x18\r \x01(\t\x12\x11\n\tcode_font\x18\x0e \x01(\t\x12\x1d\n\nfont_faces\x18\x0f \x03(\x0b\x32\t.FontFace\x12\x1e\n\nfont_sizes\x18\x10 \x01(\x0b\x32\n.FontSizes\x12!\n\x19skeleton_background_color\x18\x11 \x01(\t\x12\x18\n\x0b\x62\x61se_radius\x18\x12 \x01(\tH\x00\x88\x01\x01\x12\x1a\n\rbutton_radius\x18\x1a \x01(\tH\x01\x88\x01\x01\x12\x19\n\x0c\x62order_color\x18\x13 \x01(\tH\x02\x88\x01\x01\x12#\n\x16\x64\x61taframe_border_color\x18\x1b \x01(\tH\x03\x88\x01\x01\x12\x1f\n\x12show_widget_border\x18\x14 \x01(\x08H\x04\x88\x01\x01\x12\x17\n\nlink_color\x18\x15 \x01(\tH\x05\x88\x01\x01\x12\x1b\n\x0elink_underline\x18\x1d \x01(\x08H\x06\x88\x01\x01\x12\x1b\n\x0e\x62\x61se_font_size\x18\x16 \x01(\x05H\x07\x88\x01\x01\x12\x1d\n\x10\x62\x61se_font_weight\x18\x1e \x01(\x05H\x08\x88\x01\x01\x12\x1d\n\x10\x63ode_font_weight\x18  \x01(\x05H\t\x88\x01\x01\x12\x1b\n\x0e\x63ode_font_size\x18\x1c \x01(\tH\n\x88\x01\x01\x12\x1a\n\x12heading_font_sizes\x18# \x03(\t\x12\x1c\n\x14heading_font_weights\x18\" \x03(\x05\x12 \n\x13show_sidebar_border\x18\x17 \x01(\x08H\x0b\x88\x01\x01\x12(\n\x07sidebar\x18\x18 \x01(\x0b\x32\x12.CustomThemeConfigH\x0c\x88\x01\x01\x12\"\n\x15\x63ode_background_color\x18\x19 \x01(\tH\r\x88\x01\x01\x12.\n!dataframe_header_background_color\x18\x1f \x01(\tH\x0e\x88\x01\x01\x12 \n\x18\x63hart_categorical_colors\x18! \x03(\t\x12\x1f\n\x17\x63hart_sequential_colors\x18$ \x03(\t\" \n\tBaseTheme\x12\t\n\x05LIGHT\x10\x00\x12\x08\n\x04\x44\x41RK\x10\x01\"6\n\nFontFamily\x12\x0e\n\nSANS_SERIF\x10\x00\x12\t\n\x05SERIF\x10\x01\x12\r\n\tMONOSPACE\x10\x02\x42\x0e\n\x0c_base_radiusB\x10\n\x0e_button_radiusB\x0f\n\r_border_colorB\x19\n\x17_dataframe_border_colorB\x15\n\x13_show_widget_borderB\r\n\x0b_link_colorB\x11\n\x0f_link_underlineB\x11\n\x0f_base_font_sizeB\x13\n\x11_base_font_weightB\x13\n\x11_code_font_weightB\x11\n\x0f_code_font_sizeB\x16\n\x14_show_sidebar_borderB\n\n\x08_sidebarB\x18\n\x16_code_background_colorB$\n\"_dataframe_header_background_color\"w\n\x08\x46ontFace\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0e\n\x06\x66\x61mily\x18\x02 \x01(\t\x12\x12\n\x06weight\x18\x03 \x01(\x05\x42\x02\x18\x01\x12\x14\n\x0cweight_range\x18\x05 \x01(\t\x12\r\n\x05style\x18\x04 \x01(\t\x12\x15\n\runicode_range\x18\x06 \x01(\t\"<\n\x05Radii\x12\x1a\n\x12\x62\x61se_widget_radius\x18\x01 \x01(\x05\x12\x17\n\x0f\x63heckbox_radius\x18\x02 \x01(\x05\"T\n\tFontSizes\x12\x16\n\x0etiny_font_size\x18\x01 \x01(\x05\x12\x17\n\x0fsmall_font_size\x18\x02 \x01(\x05\x12\x16\n\x0e\x62\x61se_font_size\x18\x03 \x01(\x05\"a\n\x08UserInfo\x12\x17\n\x0finstallation_id\x18\x01 \x01(\t\x12\x1a\n\x12installation_id_v3\x18\x05 \x01(\t\x12\x1a\n\x12installation_id_v4\x18\x06 \x01(\tJ\x04\x08\x02\x10\x03\"l\n\x0f\x45nvironmentInfo\x12\x19\n\x11streamlit_version\x18\x01 \x01(\t\x12\x16\n\x0epython_version\x18\x02 \x01(\t\x12\x11\n\tserver_os\x18\x03 \x01(\t\x12\x13\n\x0bhas_display\x18\x04 \x01(\x08\x42/\n\x1c\x63om.snowflake.apps.streamlitB\x0fNewSessionProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.NewSession_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\017NewSessionProto'
  _globals['_FONTFACE'].fields_by_name['weight']._loaded_options = None
  _globals['_FONTFACE'].fields_by_name['weight']._serialized_options = b'\030\001'
  _globals['_NEWSESSION']._serialized_start=105
  _globals['_NEWSESSION']._serialized_end=398
  _globals['_INITIALIZE']._serialized_start=401
  _globals['_INITIALIZE']._serialized_end=587
  _globals['_CONFIG']._serialized_start=590
  _globals['_CONFIG']._serialized_end=869
  _globals['_CONFIG_TOOLBARMODE']._serialized_start=800
  _globals['_CONFIG_TOOLBARMODE']._serialized_end=863
  _globals['_CUSTOMTHEMECONFIG']._serialized_start=872
  _globals['_CUSTOMTHEMECONFIG']._serialized_end=2343
  _globals['_CUSTOMTHEMECONFIG_BASETHEME']._serialized_start=1940
  _globals['_CUSTOMTHEMECONFIG_BASETHEME']._serialized_end=1972
  _globals['_CUSTOMTHEMECONFIG_FONTFAMILY']._serialized_start=1974
  _globals['_CUSTOMTHEMECONFIG_FONTFAMILY']._serialized_end=2028
  _globals['_FONTFACE']._serialized_start=2345
  _globals['_FONTFACE']._serialized_end=2464
  _globals['_RADII']._serialized_start=2466
  _globals['_RADII']._serialized_end=2526
  _globals['_FONTSIZES']._serialized_start=2528
  _globals['_FONTSIZES']._serialized_end=2612
  _globals['_USERINFO']._serialized_start=2614
  _globals['_USERINFO']._serialized_end=2711
  _globals['_ENVIRONMENTINFO']._serialized_start=2713
  _globals['_ENVIRONMENTINFO']._serialized_end=2821
# @@protoc_insertion_point(module_scope)
