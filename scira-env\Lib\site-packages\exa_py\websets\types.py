# generated by datamodel-codegen:
#   filename:  schema.json
#   timestamp: 2025-05-30T02:51:12+00:00

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union, Annotated

from pydantic import AnyUrl, Field, PositiveInt
from .core.base import ExaBaseModel


class WebsetSearchBehavior(Enum):
    """
    The behavior of the Search when it is added to a Webset.

    - `override`: the search will replace the existing Items found in the Webset and evaluate them against the new criteria. Any Items that don't match the new criteria will be discarded.
    - `append`: the search will add the new Items found to the existing Webset. Any Items that don't match the new criteria will be discarded.
    """
    override = 'override'
    append = 'append'


class MonitorBehaviorSearchConfig(ExaBaseModel):
    query: str
    criteria: List[SearchCriterion]
    entity: Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ] = Field(..., title='WebsetEntity')
    count: int
    """
    The maximum number of results to find
    """
    behavior: Optional[WebsetSearchBehavior] = WebsetSearchBehavior.append
    """
    The behaviour of the Search when it is added to a Webset.
    """


class CreateCriterionParameters(ExaBaseModel):
    description: str
    """
    The description of the criterion
    """


class CreateEnrichmentParameters(ExaBaseModel):
    description: str
    """
    Provide a description of the enrichment task you want to perform to each Webset Item.
    """
    format: Optional[Format] = None
    """
    Format of the enrichment response.

    We automatically select the best format based on the description. If you want to explicitly specify the format, you can do so here.
    """
    options: Optional[List[Option]] = None
    """
    When the format is options, the different options for the enrichment agent to choose from.
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class CreateMonitorParameters(ExaBaseModel):
    webset_id: Annotated[str, Field(alias='websetId')]
    """
    The id of the Webset
    """
    cadence: MonitorCadence
    """
    How often the monitor will run
    """
    behavior: Union[MonitorBehaviorSearch, MonitorBehaviorRefresh] = Field(
        ..., discriminator='type'
    )
    """
    Behavior to perform when monitor runs
    """
    metadata: Optional[Dict[str, Any]] = None


class CreateWebhookParameters(ExaBaseModel):
    events: List[EventType]
    """
    The events to trigger the webhook
    """
    url: AnyUrl
    """
    The URL to send the webhook to
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class CreateWebsetParameters(ExaBaseModel):
    search: Optional[CreateWebsetParametersSearch] = None
    """
    Create initial search for the Webset.
    """
    imports: Annotated[Optional[List[ImportItem]], Field(alias='import')] = None
    """
    Import data from existing Websets and Imports into this Webset.
    """
    enrichments: Optional[List[CreateEnrichmentParameters]] = None
    """
    Add Enrichments for the Webset.
    """
    external_id: Annotated[Optional[str], Field(alias='externalId')] = None
    """
    The external identifier for the webset.

    You can use this to reference the Webset by your own internal identifiers.
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class CreateWebsetSearchParameters(ExaBaseModel):
    count: int
    """
    Number of Items the Search will attempt to find.

    The actual number of Items found may be less than this number depending on the query complexity.
    """
    query: str = Field(
        ...,
        examples=[
            'Marketing agencies based in the US, that focus on consumer products. Get brands worked with and city'
        ],
    )
    """
    Query describing what you are looking for.

    Any URL provided will be crawled and used as context for the search.
    """
    entity: Optional[
        Union[
            WebsetCompanyEntity,
            WebsetPersonEntity,
            WebsetArticleEntity,
            WebsetResearchPaperEntity,
            WebsetCustomEntity,
        ]
    ] = None
    """
    Entity the Webset will return results for.

    It is not required to provide it, we automatically detect the entity from all the information provided in the query.
    """
    criteria: Optional[List[CreateCriterionParameters]] = None
    """
    Criteria every item is evaluated against.

    It's not required to provide your own criteria, we automatically detect the criteria from all the information provided in the query.
    """
    exclude: Optional[List[ExcludeItem]] = None
    """
    Sources (existing imports or websets) to exclude from search results. Any results found within these sources will be omitted to prevent finding them during search.
    """
    scope: Optional[List[ScopeItem]] = None
    """
    Limit the search to specific sources (existing imports or websets). Any results found within these sources matching the search criteria will be included in the Webset.
    """
    behavior: Optional[WebsetSearchBehavior] = WebsetSearchBehavior.override
    """
    The behavior of the Search when it is added to a Webset.

    - `override`: the search will replace the existing Items found in the Webset and evaluate them against the new criteria. Any Items that don't match the new criteria will be discarded.
    - `append`: the search will add the new Items found to the existing Webset. Any Items that don't match the new criteria will be discarded.
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class WebsetSearchCriterion(ExaBaseModel):
    description: str
    """
    The description of the criterion
    """
    success_rate: Annotated[float, Field(ge=0.0, le=100.0, alias='successRate')]
    """
    Value between 0 and 100 representing the percentage of results that meet the criterion.
    """


class SearchCriterion(ExaBaseModel):
    description: str


class EnrichmentResult(ExaBaseModel):
    object: Literal['enrichment_result']
    format: WebsetEnrichmentFormat
    result: Optional[List[str]] = None
    """
    The result of the enrichment. None if the enrichment wasn't successful.
    """
    reasoning: Optional[str] = None
    """
    The reasoning for the result when an Agent is used.
    """
    references: List[Reference]
    """
    The references used to generate the result.
    """
    enrichment_id: Annotated[str, Field(alias='enrichmentId')]
    """
    The unique identifier for the enrichment
    """


class MonitorRefreshBehaviorEnrichmentsConfigEnrichments(ExaBaseModel):
    """
    Only refresh specific enrichments
    """

    ids: Optional[List[str]] = None


class EventType(Enum):
    webset_created = 'webset.created'
    webset_deleted = 'webset.deleted'
    webset_paused = 'webset.paused'
    webset_idle = 'webset.idle'
    webset_search_created = 'webset.search.created'
    webset_search_canceled = 'webset.search.canceled'
    webset_search_completed = 'webset.search.completed'
    webset_search_updated = 'webset.search.updated'
    import_created = 'import.created'
    import_completed = 'import.completed'
    import_processing = 'import.processing'
    webset_export_created = 'webset.export.created'
    webset_export_completed = 'webset.export.completed'
    webset_item_created = 'webset.item.created'
    webset_item_enriched = 'webset.item.enriched'


class Format(Enum):
    """
    Format of the enrichment response.

    We automatically select the best format based on the description. If you want to explicitly specify the format, you can do so here.
    """

    text = 'text'
    date = 'date'
    number = 'number'
    options = 'options'
    email = 'email'
    phone = 'phone'


class ImportFormat(Enum):
    """
    The format of the import.
    """
    csv = 'csv'
    webset = 'webset'


class ImportStatus(Enum):
    """
    The status of the import.
    """
    pending = 'pending'
    processing = 'processing'
    completed = 'completed'
    failed = 'failed'


class ImportFailedReason(Enum):
    """
    The reason the import failed.
    """
    invalid_format = 'invalid_format'
    invalid_file_content = 'invalid_file_content'
    missing_identifier = 'missing_identifier'


class ImportSource(Enum):
    """
    The source type for imports and excludes.
    """
    import_ = 'import'
    webset = 'webset'


class ScopeSourceType(Enum):
    """
    The source type for scope filtering.
    """
    import_ = 'import'
    webset = 'webset'


class PreviewWebsetResponseEnrichmentsFormat(Enum):
    """
    Format of the enrichment in preview response.
    """
    text = 'text'
    date = 'date'
    number = 'number'
    options = 'options'
    email = 'email'
    phone = 'phone'


class ListEventsResponse(ExaBaseModel):
    data: List[Annotated[
      Union[
          WebsetCreatedEvent,
          WebsetDeletedEvent,
          WebsetIdleEvent,
          WebsetPausedEvent,
          WebsetItemCreatedEvent,
          WebsetItemEnrichedEvent,
          WebsetSearchCreatedEvent,
          WebsetSearchUpdatedEvent,
          WebsetSearchCanceledEvent,
          WebsetSearchCompletedEvent,
      ],
      Field(discriminator='type')
  ]]
    """
    The list of events
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ListMonitorRunsResponse(ExaBaseModel):
    data: List[MonitorRun]
    """
    The list of monitor runs
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ListMonitorsResponse(ExaBaseModel):
    data: List[Monitor]
    """
    The list of monitors
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ListWebhookAttemptsResponse(ExaBaseModel):
    data: List[WebhookAttempt]
    """
    The list of webhook attempts
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ListWebhooksResponse(ExaBaseModel):
    data: List[Webhook]
    """
    The list of webhooks
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ListWebsetItemResponse(ExaBaseModel):
    data: List[WebsetItem]
    """
    The list of webset items
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more Items to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ListWebsetsResponse(ExaBaseModel):
    data: List[Webset]
    """
    The list of websets
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class ImportItem(ExaBaseModel):
    """
    Represents a source to import from.
    """
    source: ImportSource
    """
    The type of source (import or webset)
    """
    id: str
    """
    The ID of the source to import from
    """


class ExcludeItem(ExaBaseModel):
    """
    Represents a source to exclude from search results.
    """
    source: ImportSource
    """
    The type of source (import or webset)
    """
    id: str
    """
    The ID of the source to exclude
    """


class ScopeRelationship(ExaBaseModel):
    """
    Represents the relationship between entities for scoped searches.
    """
    definition: str
    """
    What the relationship of the entities you hope to find is relative to the entities contained in the provided source
    """
    limit: Optional[PositiveInt] = None
    """
    Optional limit on the number of related entities to find
    """


class ScopeItem(ExaBaseModel):
    """
    Represents a source to limit search scope to.
    """
    source: ScopeSourceType
    """
    The type of source (import)
    """
    id: str
    """
    The ID of the source to search within
    """
    relationship: Optional[ScopeRelationship] = None
    """
    Optional relationship definition for hop searches
    """


class PreviewWebsetParameters(ExaBaseModel):
    """
    Parameters for previewing a webset query.
    """
    query: str
    """
    Natural language search query describing what you are looking for.
    """
    entity: Optional[Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ]] = None
    """
    Entity used to inform the decomposition. Not required - we automatically detect 
    the entity from the query. Only use when you need more fine control.
    """


class PreviewWebsetResponseEnrichment(ExaBaseModel):
    """
    Detected enrichment in preview response.
    """
    description: str
    """
    Description of the enrichment.
    """
    format: PreviewWebsetResponseEnrichmentsFormat
    """
    Format of the enrichment.
    """
    options: Optional[List[Option]] = None
    """
    When format is options, the options detected from the query.
    """


class PreviewWebsetResponseSearchCriterion(ExaBaseModel):
    """
    Detected search criterion in preview response.
    """
    description: str
    """
    Description of the criterion.
    """


class PreviewWebsetResponseSearch(ExaBaseModel):
    """
    Search information in preview response.
    """
    entity: Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ]
    """
    Detected entity from the query.
    """
    criteria: List[PreviewWebsetResponseSearchCriterion]
    """
    Detected criteria from the query.
    """


class PreviewWebsetResponse(ExaBaseModel):
    """
    Response from previewing a webset query.
    """
    search: PreviewWebsetResponseSearch
    """
    Search analysis from the query.
    """
    enrichments: List[PreviewWebsetResponseEnrichment]
    """
    Detected enrichments from the query.
    """


class CsvImportConfig(ExaBaseModel):
    """
    Configuration for CSV imports.
    """
    identifier: Optional[int] = None
    """
    Column index containing the key identifier for the entity (e.g., URL). If not provided, will be inferred.
    """


class CreateImportParameters(ExaBaseModel):
    """
    Parameters for creating an import.
    """
    size: Optional[Annotated[float, Field(le=50000000.0)]] = None
    """
    The size of the file in bytes. Maximum size is 50 MB.
    Auto-calculated when csv_data is provided and size is not specified.
    """
    count: Optional[float] = None
    """
    The number of records to import
    Auto-calculated when csv_data is provided and count is not specified.
    """
    title: Optional[str] = None
    """
    The title of the import
    """
    format: ImportFormat
    """
    The format of the import data
    """
    entity: Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ]
    """
    The type of entity the import contains
    """
    csv: Optional[CsvImportConfig] = None
    """
    CSV-specific configuration when format is 'csv'
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class CreateImportResponse(ExaBaseModel):
    """
    Response from creating an import.
    """
    id: str
    """
    The unique identifier for the Import
    """
    object: Literal['import']
    """
    The type of object
    """
    status: ImportStatus
    """
    The status of the Import
    """
    format: ImportFormat
    """
    The format of the import
    """
    entity: Optional[Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ]] = None
    """
    The type of entity the import contains
    """
    title: str
    """
    The title of the import
    """
    count: float
    """
    The number of entities in the import
    """
    metadata: Dict[str, Any]
    """
    Set of key-value pairs associated with this object
    """
    failed_reason: Annotated[Optional[ImportFailedReason], Field(alias='failedReason')] = None
    """
    The reason the import failed, if applicable
    """
    failed_at: Annotated[Optional[datetime], Field(alias='failedAt')] = None
    """
    When the import failed, if applicable
    """
    failed_message: Annotated[Optional[str], Field(alias='failedMessage')] = None
    """
    A human readable message describing the import failure
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    When the import was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    When the import was last updated
    """
    upload_url: Annotated[str, Field(alias='uploadUrl')]
    """
    The URL to upload the file to
    """
    upload_valid_until: Annotated[str, Field(alias='uploadValidUntil')]
    """
    The date and time until the upload URL is valid
    """


class Import(ExaBaseModel):
    """
    Represents an import.
    """
    id: str
    """
    The unique identifier for the Import
    """
    object: Literal['import']
    """
    The type of object
    """
    status: ImportStatus
    """
    The status of the Import
    """
    format: ImportFormat
    """
    The format of the import
    """
    entity: Optional[Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ]] = None
    """
    The type of entity the import contains
    """
    title: str
    """
    The title of the import
    """
    count: float
    """
    The number of entities in the import
    """
    metadata: Dict[str, Any]
    """
    Set of key-value pairs associated with this object
    """
    failed_reason: Annotated[Optional[ImportFailedReason], Field(alias='failedReason')] = None
    """
    The reason the import failed, if applicable
    """
    failed_at: Annotated[Optional[datetime], Field(alias='failedAt')] = None
    """
    When the import failed, if applicable
    """
    failed_message: Annotated[Optional[str], Field(alias='failedMessage')] = None
    """
    A human readable message describing the import failure
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    When the import was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    When the import was last updated
    """


class ListImportsResponse(ExaBaseModel):
    """
    Response from listing imports.
    """
    data: List[Import]
    """
    The list of imports
    """
    has_more: Annotated[bool, Field(alias='hasMore')]
    """
    Whether there are more results to paginate through
    """
    next_cursor: Annotated[Optional[str], Field(alias='nextCursor')] = None
    """
    The cursor to use for the next page of results
    """


class UpdateImport(ExaBaseModel):
    """
    Parameters for updating an import.
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """
    title: Optional[str] = None
    """
    The title of the import
    """


class Option(ExaBaseModel):
    label: str
    """
    The label of the option
    """


class Progress(ExaBaseModel):
    """
    The progress of the search
    """

    found: float
    """
    The number of results found so far
    """
    completion: Annotated[float, Field(ge=0.0, le=100.0)]
    """
    The completion percentage of the search
    """


class Reference(ExaBaseModel):
    title: Optional[str] = None
    """
    The title of the reference
    """
    snippet: Optional[str] = None
    """
    The relevant snippet of the reference content
    """
    url: AnyUrl
    """
    The URL of the reference
    """


class Satisfied(Enum):
    """
    The satisfaction of the criterion
    """

    yes = 'yes'
    no = 'no'
    unclear = 'unclear'


class CreateWebsetParametersSearch(ExaBaseModel):
    """
    Create initial search for the Webset.
    """

    query: str = Field(
        ...,
        examples=[
            'Marketing agencies based in the US, that focus on consumer products.'
        ],
    )
    """
    Your search query.

    Use this to describe what you are looking for.

    Any URL provided will be crawled and used as context for the search.
    """
    count: Optional[int] = 10
    """
    Number of Items the Webset will attempt to find.

    The actual number of Items found may be less than this number depending on the search complexity.
    """
    entity: Optional[
        Union[
            WebsetCompanyEntity,
            WebsetPersonEntity,
            WebsetArticleEntity,
            WebsetResearchPaperEntity,
            WebsetCustomEntity,
        ]
    ] = None
    """
    Entity the Webset will return results for.

    It is not required to provide it, we automatically detect the entity from all the information provided in the query. Only use this when you need more fine control.
    """
    criteria: Optional[List[CreateCriterionParameters]] = None
    """
    Criteria every item is evaluated against.

    It's not required to provide your own criteria, we automatically detect the criteria from all the information provided in the query.
    """
    exclude: Optional[List[ExcludeItem]] = None
    """
    Sources (existing imports or websets) to exclude from search results. Any results found within these sources will be omitted to prevent finding them during search.
    """
    scope: Optional[List[ScopeItem]] = None
    """
    Limit the search to specific sources (existing imports or websets). Any results found within these sources matching the search criteria will be included in the Webset.
    """


class Source(Enum):
    """
    The source of the Item
    """

    search = 'search'
    import_ = 'import'


class MonitorRunStatus(Enum):
    """
    The status of the Monitor Run
    """

    created = 'created'
    running = 'running'
    completed = 'completed'
    canceled = 'canceled'


class MonitorStatus(Enum):
    """
    The status of the Monitor
    """

    enabled = 'enabled'
    disabled = 'disabled'


class Monitor(ExaBaseModel):
    id: str
    """
    The unique identifier for the Monitor
    """
    object: str = 'monitor'
    """
    The type of object
    """
    status: MonitorStatus
    """
    The status of the Monitor
    """
    webset_id: Annotated[str, Field(alias='websetId')]
    """
    The id of the Webset the Monitor belongs to
    """
    cadence: MonitorCadence
    """
    How often the monitor will run
    """
    behavior: Union[MonitorBehaviorSearch, MonitorBehaviorRefresh] = Field(
        ..., discriminator='type'
    )
    """
    Behavior to perform when monitor runs
    """
    last_run: Annotated[Optional[MonitorRun], Field(alias='lastRun', title='MonitorRun')] = None
    """
    The last run of the monitor
    """
    next_run_at: Annotated[Optional[datetime], Field(alias='nextRunAt')] = None
    """
    When the next run will occur
    """
    metadata: Dict[str, str]
    """
    Set of key-value pairs you want to associate with this object.
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    When the monitor was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    When the monitor was last updated
    """


class MonitorBehaviorRefresh(ExaBaseModel):
    type: Literal['refresh']
    config: Union[
        MonitorRefreshBehaviorEnrichmentsConfig, MonitorRefreshBehaviorContentsConfig
    ] = Field(..., discriminator='target')


class MonitorBehaviorSearch(ExaBaseModel):
    type: Literal['search']
    config: MonitorBehaviorSearchConfig


class MonitorCadence(ExaBaseModel):
    cron: str
    """
    Cron expression for monitor cadence (must be a valid Unix cron with 5 fields). The schedule must trigger at most once per day.
    """
    timezone: Optional[str] = 'Etc/UTC'
    """
    Timezone for the cron expression
    """


class MonitorRefreshBehaviorContentsConfig(ExaBaseModel):
    target: Literal['contents']


class MonitorRefreshBehaviorEnrichmentsConfig(ExaBaseModel):
    target: Literal['enrichments']
    enrichments: Optional[MonitorRefreshBehaviorEnrichmentsConfigEnrichments] = None


class MonitorRun(ExaBaseModel):
    id: str
    """
    The unique identifier for the Monitor Run
    """
    object: str = 'monitor_run'
    """
    The type of object
    """
    status: MonitorRunStatus
    """
    The status of the Monitor Run
    """
    monitor_id: Annotated[str, Field(alias='monitorId')]
    """
    The monitor that the run is associated with
    """
    type: Type
    """
    The type of the Monitor Run
    """
    completed_at: Annotated[Optional[datetime], Field(alias='completedAt')] = None
    """
    When the run completed
    """
    failed_at: Annotated[Optional[datetime], Field(alias='failedAt')] = None
    """
    When the run failed
    """
    canceled_at: Annotated[Optional[datetime], Field(alias='canceledAt')] = None
    """
    When the run was canceled
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    When the run was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    When the run was last updated
    """


class Type(Enum):
    """
    The type of the Monitor Run
    """

    search = 'search'
    refresh = 'refresh'


class UpdateMonitor(ExaBaseModel):
    status: Optional[MonitorStatus] = None
    """
    The status of the monitor.
    """
    metadata: Optional[Dict[str, str]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class UpdateWebhookParameters(ExaBaseModel):
    events: Optional[List[EventType]] = None
    """
    The events to trigger the webhook
    """
    url: Optional[AnyUrl] = None
    """
    The URL to send the webhook to
    """
    metadata: Optional[Dict[str, Any]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class UpdateWebsetRequest(ExaBaseModel):
    metadata: Optional[Dict[str, str]] = None
    """
    Set of key-value pairs you want to associate with this object.
    """


class Webhook(ExaBaseModel):
    id: str
    """
    The unique identifier for the webhook
    """
    object: Literal['webhook']
    status: WebhookStatus = Field(..., title='WebhookStatus')
    """
    The status of the webhook
    """
    events: List[EventType]
    """
    The events to trigger the webhook
    """
    url: AnyUrl
    """
    The URL to send the webhook to
    """
    secret: Optional[str] = None
    """
    The secret to verify the webhook signature. Only returned on Webhook creation.
    """
    metadata: Optional[Dict[str, Any]] = {}
    """
    The metadata of the webhook
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the webhook was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    The date and time the webhook was last updated
    """


class WebhookAttempt(ExaBaseModel):
    id: str
    """
    The unique identifier for the webhook attempt
    """
    object: Literal['webhook_attempt']
    event_id: Annotated[str, Field(alias='eventId')]
    """
    The unique identifier for the event
    """
    event_type: Annotated[EventType, Field(alias='eventType')]
    """
    The type of event
    """
    webhook_id: Annotated[str, Field(alias='webhookId')]
    """
    The unique identifier for the webhook
    """
    url: str
    """
    The URL that was used during the attempt
    """
    successful: bool
    """
    Whether the attempt was successful
    """
    response_headers: Annotated[Dict[str, Any], Field(alias='responseHeaders')]
    """
    The headers of the response
    """
    response_body: Annotated[Optional[str], Field(alias='responseBody')] = None
    """
    The body of the response
    """
    response_status_code: Annotated[float, Field(alias='responseStatusCode')]
    """
    The status code of the response
    """
    attempt: float
    """
    The attempt number of the webhook
    """
    attempted_at: Annotated[datetime, Field(alias='attemptedAt')]
    """
    The date and time the attempt was made
    """


class WebhookStatus(Enum):
    """
    The status of the webhook
    """

    active = 'active'
    inactive = 'inactive'


class Webset(ExaBaseModel):
    id: str
    """
    The unique identifier for the webset
    """
    object: Literal['webset']
    status: WebsetStatus = Field(..., title='WebsetStatus')
    """
    The status of the webset
    """
    external_id: Annotated[Optional[str], Field(alias='externalId')] = None
    """
    The external identifier for the webset
    """
    searches: List[WebsetSearch]
    """
    The searches that have been performed on the webset.
    """
    enrichments: List[WebsetEnrichment]
    """
    The Enrichments to apply to the Webset Items.
    """
    monitors: List[Monitor]
    """
    The Monitors for the Webset.
    """
    metadata: Optional[Dict[str, Any]] = {}
    """
    Set of key-value pairs you want to associate with this object.
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the webset was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    The date and time the webset was last updated
    """


class WebsetArticleEntity(ExaBaseModel):
    type: Literal['article']


class WebsetCompanyEntity(ExaBaseModel):
    type: Literal['company']


class WebsetCreatedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.created']
    data: Webset
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetCustomEntity(ExaBaseModel):
    type: Literal['custom']
    description: str
    """
    The description of the custom entity
    """


class WebsetDeletedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.deleted']
    data: Webset
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetEnrichment(ExaBaseModel):
    id: str
    """
    The unique identifier for the enrichment
    """
    object: Literal['webset_enrichment']
    status: WebsetEnrichmentStatus = Field(..., title='WebsetEnrichmentStatus')
    """
    The status of the enrichment
    """
    webset_id: Annotated[str, Field(alias='websetId')]
    """
    The unique identifier for the Webset this enrichment belongs to.
    """
    title: Optional[str] = None
    """
    The title of the enrichment.

    This will be automatically generated based on the description and format.
    """
    description: str
    """
    The description of the enrichment task provided during the creation of the enrichment.
    """
    format: Optional[WebsetEnrichmentFormat]
    """
    The format of the enrichment response.
    """
    options: Optional[List[WebsetEnrichmentOption]] = Field(
        None, title='WebsetEnrichmentOptions'
    )
    """
    When the format is options, the different options for the enrichment agent to choose from.
    """
    instructions: Optional[str] = None
    """
    The instructions for the enrichment Agent.

    This will be automatically generated based on the description and format.
    """
    metadata: Optional[Dict[str, Any]] = {}
    """
    The metadata of the enrichment
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the enrichment was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    The date and time the enrichment was last updated
    """


class WebsetEnrichmentFormat(Enum):
    text = 'text'
    date = 'date'
    number = 'number'
    options = 'options'
    email = 'email'
    phone = 'phone'


class WebsetEnrichmentOption(Option):
    pass


class WebsetEnrichmentStatus(Enum):
    """
    The status of the enrichment
    """

    pending = 'pending'
    canceled = 'canceled'
    completed = 'completed'


class WebsetIdleEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.idle']
    data: Webset
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetItem(ExaBaseModel):
    id: str
    """
    The unique identifier for the Webset Item
    """
    object: Literal['webset_item']
    source: Source
    """
    The source of the Item
    """
    source_id: Annotated[str, Field(alias='sourceId')]
    """
    The unique identifier for the source
    """
    webset_id: Annotated[str, Field(alias='websetId')]
    """
    The unique identifier for the Webset this Item belongs to.
    """
    properties: Union[
        WebsetItemPersonProperties,
        WebsetItemCompanyProperties,
        WebsetItemArticleProperties,
        WebsetItemResearchPaperProperties,
        WebsetItemCustomProperties,
    ]
    """
    The properties of the Item
    """
    evaluations: List[WebsetItemEvaluation]
    """
    The criteria evaluations of the item
    """
    enrichments: List[EnrichmentResult]
    """
    The enrichments results of the Webset item
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the item was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    The date and time the item was last updated
    """


class WebsetItemArticleProperties(ExaBaseModel):
    type: Literal['article']
    url: AnyUrl
    """
    The URL of the article
    """
    description: str
    """
    Short description of the relevance of the article
    """
    content: Optional[str] = None
    """
    The text content for the article
    """
    article: WebsetItemArticlePropertiesFields = Field(
        ..., title='WebsetItemArticlePropertiesFields'
    )
    """
    The article fields
    """


class WebsetItemArticlePropertiesFields(ExaBaseModel):
    author: Optional[str] = None
    """
    The author(s) of the article
    """
    published_at: Annotated[Optional[str], Field(alias='publishedAt')] = None
    """
    The date the article was published
    """


class WebsetItemCompanyProperties(ExaBaseModel):
    type: Literal['company']
    url: AnyUrl
    """
    The URL of the company website
    """
    description: str
    """
    Short description of the relevance of the company
    """
    content: Optional[str] = None
    """
    The text content of the company website
    """
    company: WebsetItemCompanyPropertiesFields = Field(
        ..., title='WebsetItemCompanyPropertiesFields'
    )
    """
    The company fields
    """


class WebsetItemCompanyPropertiesFields(ExaBaseModel):
    name: str
    """
    The name of the company
    """
    location: Optional[str] = None
    """
    The main location of the company
    """
    employees: Optional[float] = None
    """
    The number of employees of the company
    """
    industry: Optional[str] = None
    """
    The industry of the company
    """
    about: Optional[str] = None
    """
    A short description of the company
    """
    logo_url: Annotated[Optional[AnyUrl], Field(alias='logoUrl')] = None
    """
    The URL of the company logo
    """


class WebsetItemCreatedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.item.created']
    data: WebsetItem
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetItemCustomProperties(ExaBaseModel):
    type: Literal['custom']
    url: AnyUrl
    """
    The URL of the Item
    """
    description: str
    """
    Short description of the Item
    """
    content: Optional[str] = None
    """
    The text content of the Item
    """
    custom: WebsetItemCustomPropertiesFields = Field(
        ..., title='WebsetItemCustomPropertiesFields'
    )
    """
    The custom fields
    """


class WebsetItemCustomPropertiesFields(ExaBaseModel):
    author: Optional[str] = None
    """
    The author(s) of the website
    """
    published_at: Annotated[Optional[str], Field(alias='publishedAt')] = None
    """
    The date the content was published
    """


class WebsetItemEnrichedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.item.enriched']
    data: WebsetItem
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetItemEvaluation(ExaBaseModel):
    criterion: str
    """
    The description of the criterion
    """
    reasoning: str
    """
    The reasoning for the result of the evaluation
    """
    satisfied: Satisfied
    """
    The satisfaction of the criterion
    """
    references: Optional[List[Reference]] = []
    """
    The references used to evaluate the criterion
    """


class WebsetItemPersonProperties(ExaBaseModel):
    type: Literal['person']
    url: AnyUrl
    """
    The URL of the person profile
    """
    description: str
    """
    Short description of the relevance of the person
    """
    person: WebsetItemPersonPropertiesFields = Field(
        ..., title='WebsetItemPersonPropertiesFields'
    )
    """
    The person fields
    """


class WebsetItemPersonCompanyPropertiesFields(ExaBaseModel):
    name: str
    """
    The name of the company
    """
    location: Optional[str] = None
    """
    The location the person is working at the company
    """


class WebsetItemPersonPropertiesFields(ExaBaseModel):
    name: str
    """
    The name of the person
    """
    location: Optional[str] = None
    """
    The location of the person
    """
    position: Optional[str] = None
    """
    The current work position of the person
    """
    company: Optional[WebsetItemPersonCompanyPropertiesFields] = None
    """
    The company the person is working at
    """
    picture_url: Annotated[Optional[AnyUrl], Field(alias='pictureUrl')] = None
    """
    The URL of the person's picture
    """


class WebsetItemResearchPaperProperties(ExaBaseModel):
    type: Literal['research_paper']
    url: AnyUrl
    """
    The URL of the research paper
    """
    description: str
    """
    Short description of the relevance of the research paper
    """
    content: Optional[str] = None
    """
    The text content of the research paper
    """
    research_paper: WebsetItemResearchPaperPropertiesFields = Field(
        ..., title='WebsetItemResearchPaperPropertiesFields'
    )
    """
    The research paper fields
    """


class WebsetItemResearchPaperPropertiesFields(ExaBaseModel):
    author: Optional[str] = None
    """
    The author(s) of the research paper
    """
    published_at: Annotated[Optional[str], Field(alias='publishedAt')] = None
    """
    The date the research paper was published
    """


class WebsetPausedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.paused']
    data: Webset
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetPersonEntity(ExaBaseModel):
    type: Literal['person']


class WebsetResearchPaperEntity(ExaBaseModel):
    type: Literal['research_paper']


class WebsetSearchRecallConfidence(Enum):
    """
    Confidence level for search recall estimates.
    """
    high = 'high'
    medium = 'medium'
    low = 'low'


class WebsetSearchRecallExpected(ExaBaseModel):
    """
    Expected search recall information.
    """
    total: int
    """
    Total expected matches
    """
    confidence: WebsetSearchRecallConfidence
    """
    Confidence level of the estimate
    """


class WebsetSearchRecall(ExaBaseModel):
    """
    Search recall estimate information.
    """
    expected: WebsetSearchRecallExpected
    """
    Expected recall information
    """
    reasoning: str
    """
    Reasoning for the recall estimate
    """


class WebsetSearch(ExaBaseModel):
    id: str
    """
    The unique identifier for the search
    """
    object: Literal['webset_search']
    webset_id: Annotated[str, Field(alias='websetId')]
    """
    The unique identifier for the Webset this search belongs to
    """
    status: WebsetSearchStatus = Field(..., title='WebsetSearchStatus')
    """
    The status of the search
    """
    query: str
    """
    The query used to create the search.
    """
    entity: Optional[Union[
        WebsetCompanyEntity,
        WebsetPersonEntity,
        WebsetArticleEntity,
        WebsetResearchPaperEntity,
        WebsetCustomEntity,
    ]] = None
    """
    The entity the search will return results for.

    When no entity is provided during creation, we will automatically select the best entity based on the query.
    """
    criteria: List[WebsetSearchCriterion]
    """
    The criteria the search will use to evaluate the results. If not provided, we will automatically generate them for you.
    """
    count: PositiveInt
    """
    The number of results the search will attempt to find. The actual number of results may be less than this number depending on the search complexity.
    """
    behavior: Optional[WebsetSearchBehavior] = WebsetSearchBehavior.override
    """
    The behavior of the search when it is added to a Webset.

    - `override`: the search will replace the existing Items found in the Webset and evaluate them against the new criteria. Any Items that don't match the new criteria will be discarded.
    - `append`: the search will add the new Items found to the existing Webset. Any Items that don't match the new criteria will be discarded.
    """
    exclude: Optional[List[ExcludeItem]] = None
    """
    Sources (existing imports or websets) used to omit certain results to be found during the search.
    """
    scope: Optional[List[ScopeItem]] = None
    """
    The scope of the search. By default, there is no scope - thus searching the web. If provided during creation, the search will only be performed on the sources provided.
    """
    progress: Progress
    """
    The progress of the search
    """
    recall: Optional[WebsetSearchRecall] = None
    """
    Estimate of total potential matches (if requested)
    """
    metadata: Optional[Dict[str, Any]] = {}
    """
    Set of key-value pairs you want to associate with this object.
    """
    canceled_at: Annotated[Optional[datetime], Field(alias='canceledAt')] = None
    """
    The date and time the search was canceled
    """
    canceled_reason: Annotated[Optional[WebsetSearchCanceledReason], Field(alias='canceledReason')] = None
    """
    The reason the search was canceled
    """
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the search was created
    """
    updated_at: Annotated[datetime, Field(alias='updatedAt')]
    """
    The date and time the search was last updated
    """


class WebsetSearchCanceledEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.search.canceled']
    data: WebsetSearch
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetSearchCanceledReason(Enum):
    webset_deleted = 'webset_deleted'
    webset_canceled = 'webset_canceled'


class WebsetSearchCompletedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.search.completed']
    data: WebsetSearch
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetSearchCreatedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.search.created']
    data: WebsetSearch
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetSearchStatus(Enum):
    """
    The status of the search
    """

    created = 'created'
    running = 'running'
    completed = 'completed'
    canceled = 'canceled'


class WebsetSearchUpdatedEvent(ExaBaseModel):
    id: str
    """
    The unique identifier for the event
    """
    object: Literal['event']
    type: Literal['webset.search.updated']
    data: WebsetSearch
    created_at: Annotated[datetime, Field(alias='createdAt')]
    """
    The date and time the event was created
    """


class WebsetStatus(Enum):
    """
    The status of the webset
    """

    idle = 'idle'
    running = 'running'
    paused = 'paused'


class GetWebsetResponse(Webset):
    items: Optional[List[WebsetItem]] = None
    """
    The items in the webset
    """