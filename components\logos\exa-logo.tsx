export function ExaLogo() {
  return (
    <svg height="20" viewBox="0 0 278 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M161.632 53.2837H115.472C115.918 66.4186 125.061 72.7596 133.981 72.7596C142.9 72.7596 147.806 68.6833 150.371 62.682H160.851C158.064 73.2126 148.587 81.8182 133.981 81.8182C115.026 81.8182 104.545 68.0039 104.545 50C104.545 30.7506 117.256 18.4083 133.646 18.4083C151.931 18.4083 162.97 34.0343 161.632 53.2837ZM133.646 27.2404C124.615 27.2404 116.476 32.2226 115.584 44.4516H150.928C150.705 35.846 144.35 27.2404 133.646 27.2404Z"
        fill="currentColor"
      ></path>
      <path
        d="M219.201 19.4274L198.797 48.528L221.208 80.3462H209.055L192.777 57.1336L176.61 80.3462H165.014L187.09 48.9809L166.352 19.4274H178.505L193.111 40.3753L207.829 19.4274H219.201Z"
        fill="currentColor"
      ></path>
      <path
        d="M266.458 54.869V51.0191C248.061 52.944 236.354 55.6616 236.354 64.0408C236.354 69.8156 240.702 73.6655 247.949 73.6655C257.426 73.6655 266.458 69.2494 266.458 54.869ZM245.719 81.8182C234.458 81.8182 225.092 75.4772 225.092 64.2672C225.092 49.8868 241.036 45.6972 265.677 42.8664V41.3944C265.677 30.2976 259.545 26.561 252.075 26.561C243.712 26.561 238.806 31.2035 238.36 38.6768H227.88C228.883 25.5419 240.256 18.1818 251.963 18.1818C268.465 18.1818 275.935 26.2213 275.823 43.3193L275.712 57.3601C275.6 67.551 276.158 74.5713 277.273 80.3462H267.015C266.681 78.0815 266.346 75.5904 266.235 71.967C262.555 78.1948 256.311 81.8182 245.719 81.8182Z"
        fill="currentColor"
      ></path>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 0H78.1818V7.46269L44.8165 50L78.1818 92.5373V100H0V0ZM39.5825 43.1172L66.6956 7.46269H12.4695L39.5825 43.1172ZM8.79612 16.3977V46.2687H31.5111L8.79612 16.3977ZM31.5111 53.7313H8.79612V83.6023L31.5111 53.7313ZM12.4695 92.5373L39.5825 56.8828L66.6956 92.5373H12.4695Z"
        fill="#1F40ED"
      ></path>
    </svg>
  );
}
